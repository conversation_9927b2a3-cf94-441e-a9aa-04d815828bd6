package com.agoda.papi.pricing.payment.constants

object Constants {
  val defaultPrecision = 3
}

/**
  * Configuration for payment method-specific decimal precision overrides.
  * Maps payment method IDs to their required decimal places for customer-facing amounts.
  */
object PaymentMethodDecimalConfig {
  private val paymentMethodDecimalMap: Map[Int, Int] = Map(
    249 -> 0, // Payment method 249 requires 0 decimal places (whole integers)
  )

  def getDecimalPrecision(paymentMethodId: Option[Int], defaultDecimalPlaces: Int): Int =
    paymentMethodId.flatMap(paymentMethodDecimalMap.get).getOrElse(defaultDecimalPlaces)

  def hasDecimalOverride(paymentMethodId: Option[Int]): Boolean =
    paymentMethodId.exists(paymentMethodDecimalMap.contains)
}
